"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
const SHIFT_LEFT_32 = (1 << 16) * (1 << 16);
const SHIFT_RIGHT_32 = 1 / SHIFT_LEFT_32;
const UNKNOWN_PLP_LEN = Buffer.from([0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff]);
const ZERO_LENGTH_BUFFER = Buffer.alloc(0);
/**
  A Buffer-like class that tracks position.

  As values are written, the position advances by the size of the written data.
  When writing, automatically allocates new buffers if there's not enough space.
 */
class WritableTrackingBuffer {
  constructor(initialSize, encoding, doubleSizeGrowth) {
    this.initialSize = initialSize;
    this.encoding = encoding || 'ucs2';
    this.doubleSizeGrowth = doubleSizeGrowth || false;
    this.buffer = Buffer.alloc(this.initialSize, 0);
    this.compositeBuffer = ZERO_LENGTH_BUFFER;
    this.position = 0;
  }
  get data() {
    this.newBuffer(0);
    return this.compositeBuffer;
  }
  copyFrom(buffer) {
    const length = buffer.length;
    this.makeRoomFor(length);
    buffer.copy(this.buffer, this.position);
    this.position += length;
  }
  makeRoomFor(requiredLength) {
    if (this.buffer.length - this.position < requiredLength) {
      if (this.doubleSizeGrowth) {
        let size = Math.max(128, this.buffer.length * 2);
        while (size < requiredLength) {
          size *= 2;
        }
        this.newBuffer(size);
      } else {
        this.newBuffer(requiredLength);
      }
    }
  }
  newBuffer(size) {
    const buffer = this.buffer.slice(0, this.position);
    this.compositeBuffer = Buffer.concat([this.compositeBuffer, buffer]);
    this.buffer = size === 0 ? ZERO_LENGTH_BUFFER : Buffer.alloc(size, 0);
    this.position = 0;
  }
  writeUInt8(value) {
    const length = 1;
    this.makeRoomFor(length);
    this.buffer.writeUInt8(value, this.position);
    this.position += length;
  }
  writeUInt16LE(value) {
    const length = 2;
    this.makeRoomFor(length);
    this.buffer.writeUInt16LE(value, this.position);
    this.position += length;
  }
  writeUShort(value) {
    this.writeUInt16LE(value);
  }
  writeUInt16BE(value) {
    const length = 2;
    this.makeRoomFor(length);
    this.buffer.writeUInt16BE(value, this.position);
    this.position += length;
  }
  writeUInt24LE(value) {
    const length = 3;
    this.makeRoomFor(length);
    this.buffer[this.position + 2] = value >>> 16 & 0xff;
    this.buffer[this.position + 1] = value >>> 8 & 0xff;
    this.buffer[this.position] = value & 0xff;
    this.position += length;
  }
  writeUInt32LE(value) {
    const length = 4;
    this.makeRoomFor(length);
    this.buffer.writeUInt32LE(value, this.position);
    this.position += length;
  }
  writeBigInt64LE(value) {
    const length = 8;
    this.makeRoomFor(length);
    this.buffer.writeBigInt64LE(value, this.position);
    this.position += length;
  }
  writeInt64LE(value) {
    this.writeBigInt64LE(BigInt(value));
  }
  writeUInt64LE(value) {
    this.writeBigUInt64LE(BigInt(value));
  }
  writeBigUInt64LE(value) {
    const length = 8;
    this.makeRoomFor(length);
    this.buffer.writeBigUInt64LE(value, this.position);
    this.position += length;
  }
  writeUInt32BE(value) {
    const length = 4;
    this.makeRoomFor(length);
    this.buffer.writeUInt32BE(value, this.position);
    this.position += length;
  }
  writeUInt40LE(value) {
    // inspired by https://github.com/dpw/node-buffer-more-ints
    this.writeInt32LE(value & -1);
    this.writeUInt8(Math.floor(value * SHIFT_RIGHT_32));
  }
  writeInt8(value) {
    const length = 1;
    this.makeRoomFor(length);
    this.buffer.writeInt8(value, this.position);
    this.position += length;
  }
  writeInt16LE(value) {
    const length = 2;
    this.makeRoomFor(length);
    this.buffer.writeInt16LE(value, this.position);
    this.position += length;
  }
  writeInt16BE(value) {
    const length = 2;
    this.makeRoomFor(length);
    this.buffer.writeInt16BE(value, this.position);
    this.position += length;
  }
  writeInt32LE(value) {
    const length = 4;
    this.makeRoomFor(length);
    this.buffer.writeInt32LE(value, this.position);
    this.position += length;
  }
  writeInt32BE(value) {
    const length = 4;
    this.makeRoomFor(length);
    this.buffer.writeInt32BE(value, this.position);
    this.position += length;
  }
  writeFloatLE(value) {
    const length = 4;
    this.makeRoomFor(length);
    this.buffer.writeFloatLE(value, this.position);
    this.position += length;
  }
  writeDoubleLE(value) {
    const length = 8;
    this.makeRoomFor(length);
    this.buffer.writeDoubleLE(value, this.position);
    this.position += length;
  }
  writeString(value, encoding) {
    if (encoding == null) {
      encoding = this.encoding;
    }
    const length = Buffer.byteLength(value, encoding);
    this.makeRoomFor(length);

    // $FlowFixMe https://github.com/facebook/flow/pull/5398
    this.buffer.write(value, this.position, encoding);
    this.position += length;
  }
  writeBVarchar(value, encoding) {
    this.writeUInt8(value.length);
    this.writeString(value, encoding);
  }
  writeUsVarchar(value, encoding) {
    this.writeUInt16LE(value.length);
    this.writeString(value, encoding);
  }

  // TODO: Figure out what types are passed in other than `Buffer`
  writeUsVarbyte(value, encoding) {
    if (encoding == null) {
      encoding = this.encoding;
    }
    let length;
    if (value instanceof Buffer) {
      length = value.length;
    } else {
      value = value.toString();
      length = Buffer.byteLength(value, encoding);
    }
    this.writeUInt16LE(length);
    if (value instanceof Buffer) {
      this.writeBuffer(value);
    } else {
      this.makeRoomFor(length);
      // $FlowFixMe https://github.com/facebook/flow/pull/5398
      this.buffer.write(value, this.position, encoding);
      this.position += length;
    }
  }
  writePLPBody(value, encoding) {
    if (encoding == null) {
      encoding = this.encoding;
    }
    let length;
    if (value instanceof Buffer) {
      length = value.length;
    } else {
      value = value.toString();
      length = Buffer.byteLength(value, encoding);
    }

    // Length of all chunks.
    // this.writeUInt64LE(length);
    // unknown seems to work better here - might revisit later.
    this.writeBuffer(UNKNOWN_PLP_LEN);

    // In the UNKNOWN_PLP_LEN case, the data is represented as a series of zero or more chunks.
    if (length > 0) {
      // One chunk.
      this.writeUInt32LE(length);
      if (value instanceof Buffer) {
        this.writeBuffer(value);
      } else {
        this.makeRoomFor(length);
        this.buffer.write(value, this.position, encoding);
        this.position += length;
      }
    }

    // PLP_TERMINATOR (no more chunks).
    this.writeUInt32LE(0);
  }
  writeBuffer(value) {
    const length = value.length;
    this.makeRoomFor(length);
    value.copy(this.buffer, this.position);
    this.position += length;
  }
  writeMoney(value) {
    this.writeInt32LE(Math.floor(value * SHIFT_RIGHT_32));
    this.writeInt32LE(value & -1);
  }
}
var _default = exports.default = WritableTrackingBuffer;
module.exports = WritableTrackingBuffer;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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