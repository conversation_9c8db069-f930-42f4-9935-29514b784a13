<?php
/**
 * sse_mod_logistica.php
 * Server-Sent Events handler specifically for mod_logistica.php
 * Independent from sse_logistica.php - handles all 4 tables in mod_logistica.php
 */

header('Content-Type: text/event-stream');
header('Cache-Control: no-cache');
header('Connection: keep-alive');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Cache-Control');

// Include database connection
include("con_db.php");

// Get user ID from request
$userId = isset($_GET['user_id']) ? intval($_GET['user_id']) : 0;

// Set execution time limits
set_time_limit(600);
$max_execution_time = 600;
$start_time = time();

/**
 * Send SSE message to client
 */
function sendSSEMessage($data) {
    echo "data: " . json_encode($data) . "\n\n";
    ob_flush();
    flush();
}

/**
 * Log SSE activity for debugging
 */
function logSSEActivity($message, $data = []) {
    $logData = [
        'timestamp' => date('Y-m-d H:i:s'),
        'message' => $message,
        'data' => $data
    ];
    error_log("SSE_MOD_LOGISTICA: " . json_encode($logData));
}

// Validate user ID
if ($userId <= 0) {
    sendSSEMessage(['type' => 'error', 'message' => 'Invalid user ID']);
    exit;
}

// Get initial last ID from tb_logis_movimientos
$query = "SELECT MAX(id) as last_id FROM tb_logis_movimientos";
$result = $conex->query($query);
$row = $result->fetch_assoc();
$lastId = $row['last_id'] ?? 0;

sendSSEMessage(['type' => 'log', 'message' => "SSE Mod Logistica started for user $userId with last ID: $lastId"]);
logSSEActivity("SSE started", ['user_id' => $userId, 'last_id' => $lastId]);

// Main SSE loop
while (time() - $start_time < $max_execution_time) {
    
    // Check for new movements
    $newMovements = false;
    
    // ==========================================
    // 1. DIRECTA TABLE (directaTableBody)
    // ==========================================
    $queryDirecta = "SELECT A.*, tp_logis.Semantica 
                     FROM tb_logis_movimientos A 
                     LEFT JOIN tp_logis_movimientos tp_logis ON tp_logis.id = A.id_movimiento 
                     WHERE A.id > $lastId
                     AND id_movimiento NOT IN (2, 3, 9, 10, 11, 18, 23) -- Exclude rejections and transfers for directa
                     AND (
                         (id_movimiento = 15 AND id_tecnico_origen = $userId) OR
                         (id_movimiento IN (4, 19) AND ticket = $userId) OR
                         (id_movimiento NOT IN (0, 15, 21) AND id_tecnico_destino = $userId) OR
                         (id_movimiento IN (12, 24) AND ticket = $userId)
                     )";

    $resultDirecta = $conex->query($queryDirecta);
    
    if ($resultDirecta && $resultDirecta->num_rows > 0) {
        while ($row = $resultDirecta->fetch_assoc()) {
            $lastId = max($lastId, $row['id']);
            $newMovements = true;

            // Determine status class based on semantics
            $semantica = $row['Semantica'] ?? 'Pendiente';
            $clase_estado = determineStatusClass($semantica);

            // Verificar si es un escalamiento/transferencia que debe eliminar la fila original
            $escalamientos = [2, 9, 10]; // RECHAZA, RECHAZA_BODEGA, RECHAZA_SUPER

            if (in_array($row['id_movimiento'], $escalamientos)) {
                // Es un escalamiento - eliminar la fila de la tabla directa
                $data = [
                    'type' => 'remove',
                    'table' => 'TablaDirecta',
                    'target_tbody' => 'directaTableBody',
                    'serial' => $row['serie'],
                    'id_movimiento' => $row['id_movimiento'],
                    'semantica' => $semantica
                ];

                sendSSEMessage($data);
                logSSEActivity("Directa table remove (escalamiento)", ['serie' => $row['serie'], 'id_movimiento' => $row['id_movimiento']]);
            } else {
                // Movimiento normal - agregar/actualizar fila
                $html = generateDirectaTableRow($row, $clase_estado, $semantica);

                $data = [
                    'type' => 'update',
                    'table' => 'TablaDirecta',
                    'target_tbody' => 'directaTableBody',
                    'serial' => $row['serie'],
                    'id_movimiento' => $row['id_movimiento'],
                    'semantica' => $semantica,
                    'html' => $html
                ];

                sendSSEMessage($data);
                logSSEActivity("Directa table update", ['serie' => $row['serie'], 'id_movimiento' => $row['id_movimiento']]);
            }
        }
    }

    // ==========================================
    // 2. FALTANTE TABLE (faltanteTableBody)
    // ==========================================
    $queryFaltante = "SELECT A.*, tp_logis.Semantica 
                      FROM tb_logis_movimientos A 
                      LEFT JOIN tp_logis_movimientos tp_logis ON tp_logis.id = A.id_movimiento 
                      WHERE A.id > $lastId
                      AND id_movimiento IN (20) -- Faltante movements
                      AND (
                          id_tecnico_destino = $userId OR 
                          id_tecnico_origen = $userId OR
                          ticket = $userId
                      )";

    $resultFaltante = $conex->query($queryFaltante);
    
    if ($resultFaltante && $resultFaltante->num_rows > 0) {
        while ($row = $resultFaltante->fetch_assoc()) {
            $lastId = max($lastId, $row['id']);
            $newMovements = true;
            
            $semantica = $row['Semantica'] ?? 'Faltante';
            $clase_estado = determineStatusClass($semantica);
            
            // Generate HTML for faltante table
            $html = generateFaltanteTableRow($row, $clase_estado, $semantica);
            
            $data = [
                'type' => 'update',
                'table' => 'TablaFaltante',
                'target_tbody' => 'faltanteTableBody',
                'serial' => $row['serie'],
                'id_movimiento' => $row['id_movimiento'],
                'semantica' => $semantica,
                'html' => $html
            ];
            
            sendSSEMessage($data);
            logSSEActivity("Faltante table update", ['serie' => $row['serie'], 'id_movimiento' => $row['id_movimiento']]);
        }
    }

    // ==========================================
    // 3. RECEPCION TABLE (recepcionTableBody)
    // ==========================================
    // IMPORTANT: Include id_movimiento = 9 for rejections to appear in reception table
    // EXCLUDED: id_movimiento = 2 (rechazos técnico) - no deben reaparecer después de rechazar
    $queryRecepcion = "SELECT A.*, tp_logis.Semantica 
                       FROM tb_logis_movimientos A 
                       LEFT JOIN tp_logis_movimientos tp_logis ON tp_logis.id = A.id_movimiento 
                       WHERE A.id > $lastId
                       AND (
                           (id_movimiento IN (0, 15, 21) AND id_tecnico_destino = $userId) OR
                           (id_movimiento = 9 AND id_tecnico_destino = $userId) OR -- Include rejections
                           (id_movimiento = 24 AND ticket = $userId)
                       )";

    $resultRecepcion = $conex->query($queryRecepcion);
    
    if ($resultRecepcion && $resultRecepcion->num_rows > 0) {
        while ($row = $resultRecepcion->fetch_assoc()) {
            $lastId = max($lastId, $row['id']);
            $newMovements = true;
            
            $semantica = $row['Semantica'] ?? 'Pendiente';
            $clase_estado = determineStatusClass($semantica);
            
            // Generate HTML for recepcion table
            $html = generateRecepcionTableRow($row, $clase_estado, $semantica);
            
            $data = [
                'type' => 'update',
                'table' => 'tablaAsignacion',
                'target_tbody' => 'recepcionTableBody',
                'serial' => $row['serie'],
                'id_movimiento' => $row['id_movimiento'],
                'semantica' => $semantica,
                'html' => $html
            ];
            
            sendSSEMessage($data);
            logSSEActivity("Recepcion table update", ['serie' => $row['serie'], 'id_movimiento' => $row['id_movimiento']]);
        }
    }

    // ==========================================
    // 4. REVERSA TABLE (reversaTableBody)
    // ==========================================
    $queryReversa = "SELECT A.*, tp_logis.Semantica 
                     FROM tb_logis_movimientos A 
                     LEFT JOIN tp_logis_movimientos tp_logis ON tp_logis.id = A.id_movimiento 
                     WHERE A.id > $lastId
                     AND (
                         (id_movimiento IN (6, 7, 11, 23) AND (id_tecnico_destino = $userId OR id_tecnico_origen = $userId)) OR
                         (id_movimiento IN (22, 12, 11) AND ticket = $userId)
                     )";

    $resultReversa = $conex->query($queryReversa);
    
    if ($resultReversa && $resultReversa->num_rows > 0) {
        while ($row = $resultReversa->fetch_assoc()) {
            $lastId = max($lastId, $row['id']);
            $newMovements = true;
            
            $semantica = $row['Semantica'] ?? 'Reversa';
            $clase_estado = determineStatusClass($semantica);
            
            // Generate HTML for reversa table
            $html = generateReversaTableRow($row, $clase_estado, $semantica);
            
            $data = [
                'type' => 'update',
                'table' => 'poolBodegaReversa',
                'target_tbody' => 'reversaTableBody',
                'serial' => $row['serie'],
                'id_movimiento' => $row['id_movimiento'],
                'semantica' => $semantica,
                'html' => $html
            ];
            
            sendSSEMessage($data);
            logSSEActivity("Reversa table update", ['serie' => $row['serie'], 'id_movimiento' => $row['id_movimiento']]);
        }
    }

    // Send keepalive message every 30 seconds
    if ((time() - $start_time) % 30 == 0) {
        sendSSEMessage(['type' => 'keepalive', 'message' => 'Connection active', 'timestamp' => time()]);
    }

    // Sleep for 1 second before next iteration
    usleep(1000000);
}

// Connection timeout
sendSSEMessage(['type' => 'log', 'message' => 'SSE connection timeout reached']);
logSSEActivity("SSE connection timeout");

/**
 * Determine CSS class based on semantics - Updated to match tables.js logic exactly
 */
function determineStatusClass($semantica) {
    $estadoLower = strtolower(trim($semantica));

    // Caso especial para Rechaza bodega - naranja oscuro
    if ($estadoLower === 'rechaza bodega' || $estadoLower === 'rechaza bodega justificación') {
        return 'rechaza-bodega';
    }

    // Verificar si es un estado de rechazo (contiene la palabra 'rechaza')
    if (strpos($estadoLower, 'rechaza') !== false) {
        return 'rechazo';
    }

    // Verificar casos específicos de rechazo por ID conocidos
    if ($estadoLower === 'faltante' ||
        strpos($estadoLower, 'rechazo') !== false ||
        $estadoLower === 'rechaza tecnico' ||
        $estadoLower === 'rechaza supervisor' ||
        $estadoLower === 'rechaza supervisor reversa') {
        return 'rechazo';
    }

    // Estado disponible - verde con texto blanco
    if ($estadoLower === 'disponible') {
        return 'disponible';
    }

    // Todos los demás estados - amarillo con texto negro
    return 'default';
}

/**
 * Generate HTML row for directa table - UPDATED to match tables.js exactly
 */
function generateDirectaTableRow($row, $clase_estado, $semantica) {
    $html = '<tr data-type="directa" data-serial="' . htmlspecialchars($row['serie']) . '">';
    $html .= '<td>' . htmlspecialchars($row['serie']) . '</td>';
    $html .= '<td><span class="status-badge ' . $clase_estado . '">' . htmlspecialchars($semantica) . '</span></td>';
    $html .= '<td>';
    $html .= '<div class="action-buttons-container">';
    $html .= '<button class="action-btn historial-btn" title="Ver Historial" data-serie="' . htmlspecialchars($row['serie']) . '">';
    $html .= '<i class="bi bi-clock-history"></i>';
    $html .= '</button>';
    $html .= '<button class="action-btn declarar-btn" title="Declarar" data-serie="' . htmlspecialchars($row['serie']) . '" data-id-movimiento="' . $row['id_movimiento'] . '">';
    $html .= '<i class="bi bi-clipboard-check"></i>';
    $html .= '</button>';
    $html .= '<button class="action-btn justificar-btn transfiere-btn" title="Transferir" data-serie="' . htmlspecialchars($row['serie']) . '" ';
    $html .= 'onclick="redirigirEnTransferencia(\'' . htmlspecialchars($row['serie']) . '\', \'' . htmlspecialchars($row['Item'] ?? $row['serie']) . '\', \'' . $row['id_movimiento'] . '\', \'TRANSFIERE\')">';
    $html .= '<i class="bi bi-send-fill"></i>';
    $html .= '</button>';
    $html .= '</div>';
    $html .= '</td>';
    $html .= '</tr>';
    return $html;
}

/**
 * Generate HTML row for faltante table - UPDATED to match tables.js exactly
 */
function generateFaltanteTableRow($row, $clase_estado, $semantica) {
    $html = '<tr data-type="faltante" data-serial="' . htmlspecialchars($row['serie']) . '">';
    $html .= '<td>' . htmlspecialchars($row['serie']) . '</td>';
    $html .= '<td><span class="status-badge ' . $clase_estado . '">' . htmlspecialchars($semantica) . '</span></td>';
    $html .= '<td>';
    $html .= '<div class="action-buttons-container">';
    $html .= '<button class="action-btn historial-btn" title="Ver Historial" data-serie="' . htmlspecialchars($row['serie']) . '">';
    $html .= '<i class="bi bi-clock-history"></i>';
    $html .= '</button>';
    $html .= '<button class="action-btn declarar-btn" title="Declarar" data-serie="' . htmlspecialchars($row['serie']) . '" data-id-movimiento="' . $row['id_movimiento'] . '">';
    $html .= '<i class="bi bi-clipboard-check"></i>';
    $html .= '</button>';
    $html .= '<button class="action-btn justificar-btn" title="Justificar" data-serie="' . htmlspecialchars($row['serie']) . '">';
    $html .= '<i class="bi bi-shield-check"></i>';
    $html .= '</button>';
    $html .= '</div>';
    $html .= '</td>';
    $html .= '</tr>';
    return $html;
}

/**
 * Generate HTML row for recepcion table - UPDATED to match tables.js exactly
 */
function generateRecepcionTableRow($row, $clase_estado, $semantica) {
    $idMovimiento = $row['id_movimiento'];
    $idTecnicoDest = $row['id_tecnico_destino'];
    $variableCondicion = ($idMovimiento == 15) ? "Si" : $idTecnicoDest;

    $html = '<tr data-type="recepcion" data-serial="' . htmlspecialchars($row['serie']) . '">';
    $html .= '<td>' . htmlspecialchars($row['serie']) . '</td>';
    $html .= '<td><span class="status-badge ' . $clase_estado . '">' . htmlspecialchars($semantica) . '</span></td>';
    $html .= '<td>';
    $html .= '<div class="action-buttons-container">';
    $html .= '<button class="action-btn historial-btn" title="Ver Historial" data-serie="' . htmlspecialchars($row['serie']) . '">';
    $html .= '<i class="bi bi-clock-history"></i>';
    $html .= '</button>';
    $html .= '<button class="action-btn aceptar-btn btn btn-success" title="Aceptar Material" ';
    $html .= 'onclick="actualizarRegistro(\'' . htmlspecialchars($row['serie']) . '\', \'' . $variableCondicion . '\', \'' . $idMovimiento . '\', \'ACEPTA\')">';
    $html .= '<i class="bi bi-check-circle"></i>';
    $html .= '</button>';
    $html .= '<button class="action-btn justificar-btn" title="Rechazar Material" ';
    $html .= 'onclick="rechazoMaterial(\'' . htmlspecialchars($row['serie']) . '\', \'' . $variableCondicion . '\', \'' . $idTecnicoDest . '\', \'RECHAZA\')">';
    $html .= '<i class="bi bi-journal-x"></i>';
    $html .= '</button>';
    $html .= '</div>';
    $html .= '</td>';
    $html .= '</tr>';
    return $html;
}

/**
 * Generate HTML row for reversa table - UPDATED to match tables.js exactly
 */
function generateReversaTableRow($row, $clase_estado, $semantica) {
    $html = '<tr data-type="reversa" data-serial="' . htmlspecialchars($row['serie']) . '">';
    $html .= '<td>' . htmlspecialchars($row['serie']) . '</td>';
    $html .= '<td><span class="status-badge ' . $clase_estado . '">' . htmlspecialchars($semantica) . '</span></td>';
    $html .= '<td>';
    $html .= '<div class="action-buttons-container">';
    $html .= '<button class="action-btn historial-btn" title="Ver Historial" data-serie="' . htmlspecialchars($row['serie']) . '">';
    $html .= '<i class="bi bi-clock-history"></i>';
    $html .= '</button>';
    $html .= '<button class="action-btn declarar-rev-btn" title="Declarar Entrega" data-serie="' . htmlspecialchars($row['serie']) . '" data-id-movimiento="' . $row['id_movimiento'] . '">';
    $html .= '<i class="bi bi-box-seam"></i>';
    $html .= '</button>';
    $html .= '<button class="action-btn transferir-rev-btn" title="A Supervisor" data-serie="' . htmlspecialchars($row['serie']) . '" data-id-movimiento="' . $row['id_movimiento'] . '">';
    $html .= '<i class="bi bi-send"></i>';
    $html .= '</button>';
    $html .= '</div>';
    $html .= '</td>';
    $html .= '</tr>';
    return $html;
}

?>
