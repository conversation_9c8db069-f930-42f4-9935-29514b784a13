/**
 * validation.js
 * Sistema de validación para formularios del módulo de logística
 * 
 * Contiene todas las funciones de validación, tanto para validación
 * en tiempo real como para validación completa antes del envío.
 */

// Función principal para validar formularios antes del envío
function validarFormulario(tipo) {
    let isValid = true;
    const errores = [];

    switch (tipo) {
        case 'transferencia':
            isValid = validarTransferencia(errores);
            break;
        case 'instalacion':
            isValid = validarInstalacion(errores);
            break;
        case 'transferencia_reversa':
            isValid = validarTransferenciaReversa(errores);
            break;
        case 'declaracion_reversa':
            isValid = validarDeclaracionReversa(errores);
            break;
        default:
            ModLogistica.errorLog('Tipo de formulario no reconocido:', tipo);
            return false;
    }

    if (!isValid) {
        mostrarErroresValidacion(errores);
    }

    return isValid;
}

// Validaciones específicas para transferencia
function validarTransferencia(errores) {
    let isValid = true;

    // Validar serie
    const serie = document.getElementById('serie_tran').value.trim();
    if (!serie) {
        errores.push('Serie es requerida');
        marcarCampoInvalido('serie_tran', 'Por favor seleccione una serie');
        isValid = false;
    } else {
        marcarCampoValido('serie_tran');
    }

    // Validar motivo si está visible
    const motivoContainer = document.getElementById('motivo_tran_contain');
    if (motivoContainer && motivoContainer.style.display !== 'none') {
        const motivo = document.getElementById('motivo_tran').value.trim();
        const minLength = ModLogistica.getConfig('validation.text.motivoMinLength') || 10;
        
        if (!motivo || motivo.length < minLength) {
            errores.push(`Motivo debe tener al menos ${minLength} caracteres`);
            marcarCampoInvalido('motivo_tran', `Por favor ingrese un motivo válido (mínimo ${minLength} caracteres)`);
            isValid = false;
        } else {
            marcarCampoValido('motivo_tran');
        }
    }

    // Validar técnico destino si está visible
    const tecnicoContainer = document.getElementById('tecnicoTransf');
    if (tecnicoContainer && tecnicoContainer.style.display !== 'none') {
        const usuarioDestino = document.getElementById('usuario_destino').value;
        if (!usuarioDestino) {
            errores.push('Técnico de destino es requerido');
            marcarCampoInvalido('usuario_destino', 'Por favor seleccione un técnico de destino');
            isValid = false;
        } else {
            marcarCampoValido('usuario_destino');
        }
    }

    // Validar archivo si es requerido
    const archivoContainer = document.getElementById('divArchivo');
    if (archivoContainer && archivoContainer.style.display !== 'none') {
        const archivo = document.getElementById('fileInventario');
        if (archivo && archivo.files.length > 0) {
            if (!validarArchivo(archivo, 'fileInventario')) {
                isValid = false;
            }
        }
    }

    return isValid;
}

// Validaciones específicas para instalación
function validarInstalacion(errores) {
    let isValid = true;

    // Validar serie
    const serie = document.getElementById('serie_insta').value.trim();
    if (!serie) {
        errores.push('Serie es requerida');
        marcarCampoInvalido('serie_insta', 'Por favor seleccione una serie');
        isValid = false;
    } else {
        marcarCampoValido('serie_insta');
    }

    // Validar orden de trabajo
    const orden = document.getElementById('formOT_insta').value.trim();
    const patronOrden = ModLogistica.getConfig('validation.patterns.ordenTrabajo') || /^[0-9A-Za-z\-]+$/;
    
    if (!orden) {
        errores.push('Orden de trabajo es requerida');
        marcarCampoInvalido('formOT_insta', 'Por favor ingrese una orden de trabajo válida');
        isValid = false;
    } else if (!patronOrden.test(orden)) {
        errores.push('Orden de trabajo contiene caracteres inválidos');
        marcarCampoInvalido('formOT_insta', 'Solo se permiten números, letras y guiones');
        isValid = false;
    } else {
        marcarCampoValido('formOT_insta');
    }

    // Validar RUT si se proporciona
    const rut = document.getElementById('rut_insta').value.trim();
    const patronRut = ModLogistica.getConfig('validation.patterns.rut') || /^[0-9]{7,8}-[0-9kK]{1}$/;
    
    if (rut && !patronRut.test(rut)) {
        errores.push('Formato de RUT inválido');
        marcarCampoInvalido('rut_insta', 'Por favor ingrese un RUT válido (formato: 12345678-9)');
        isValid = false;
    } else {
        marcarCampoValido('rut_insta');
    }

    // Validar observaciones (ahora opcional)
    const obs = document.getElementById('obs_insta').value.trim();
    const maxLength = ModLogistica.getConfig('validation.text.observacionesMaxLength') || 500;
    
    // Solo validar si se proporciona texto
    if (obs && obs.length > maxLength) {
        errores.push(`Observaciones no pueden exceder ${maxLength} caracteres`);
        marcarCampoInvalido('obs_insta', `Las observaciones no pueden exceder ${maxLength} caracteres`);
        isValid = false;
    } else {
        marcarCampoValido('obs_insta');
    }

    // Validar archivo si se proporciona
    const archivo = document.getElementById('fileInsta');
    if (archivo && archivo.files.length > 0) {
        if (!validarArchivo(archivo, 'fileInsta')) {
            isValid = false;
        }
    }

    return isValid;
}

// Validaciones específicas para transferencia reversa
function validarTransferenciaReversa(errores) {
    let isValid = true;

    // Validar serie
    const serie = document.getElementById('serie_trans_rever').value.trim();
    if (!serie) {
        errores.push('Serie es requerida');
        marcarCampoInvalido('serie_trans_rever', 'Por favor seleccione una serie');
        isValid = false;
    } else {
        marcarCampoValido('serie_trans_rever');
    }

    // Validar orden de trabajo
    const orden = document.getElementById('ordenReversa').value.trim();
    const patronOrden = ModLogistica.getConfig('validation.patterns.ordenTrabajo');

    if (!orden) {
        errores.push('Orden de trabajo es requerida');
        marcarCampoInvalido('ordenReversa', 'Por favor ingrese una orden de trabajo válida');
        isValid = false;
    } else if (!patronOrden.test(orden)) {
        errores.push('Orden de trabajo contiene caracteres inválidos');
        marcarCampoInvalido('ordenReversa', 'Solo se permiten números, letras y guiones');
        isValid = false;
    } else {
        marcarCampoValido('ordenReversa');
    }

    // Validar RUT si se proporciona
    const rut = document.getElementById('rutReversa').value.trim();
    const patronRut = ModLogistica.getConfig('validation.patterns.rut');

    if (rut && !patronRut.test(rut)) {
        errores.push('Formato de RUT inválido');
        marcarCampoInvalido('rutReversa', 'Por favor ingrese un RUT válido (formato: 12345678-9)');
        isValid = false;
    } else {
        marcarCampoValido('rutReversa');
    }

    // Validar motivo
    const motivo = document.getElementById('listReversa').value;
    if (!motivo) {
        errores.push('Motivo es requerido');
        marcarCampoInvalido('listReversa', 'Por favor seleccione un motivo');
        isValid = false;
    } else {
        marcarCampoValido('listReversa');
    }

    // Validar serie nueva
    const serieNueva = document.getElementById('serieNewReversa').value.trim();
    const patronSerie = ModLogistica.getConfig('validation.patterns.serie');

    if (!serieNueva) {
        errores.push('Serie física retirada es requerida');
        marcarCampoInvalido('serieNewReversa', 'Por favor ingrese una serie válida');
        isValid = false;
    } else if (!patronSerie.test(serieNueva)) {
        errores.push('Serie contiene caracteres inválidos');
        marcarCampoInvalido('serieNewReversa', 'Solo se permiten letras, números y guiones');
        isValid = false;
    } else {
        marcarCampoValido('serieNewReversa');
    }

    // Validar observaciones
    const obs = document.getElementById('obs_rev_tra').value.trim();
    const minLength = ModLogistica.getConfig('validation.text.observacionesMinLength');
    const maxLength = ModLogistica.getConfig('validation.text.observacionesMaxLength');

    if (!obs || obs.length < minLength) {
        errores.push(`Observaciones deben tener al menos ${minLength} caracteres`);
        marcarCampoInvalido('obs_rev_tra', `Por favor ingrese observaciones válidas (mínimo ${minLength} caracteres)`);
        isValid = false;
    } else if (obs.length > maxLength) {
        errores.push(`Observaciones no pueden exceder ${maxLength} caracteres`);
        marcarCampoInvalido('obs_rev_tra', `Las observaciones no pueden exceder ${maxLength} caracteres`);
        isValid = false;
    } else {
        marcarCampoValido('obs_rev_tra');
    }

    // Validar archivo si se proporciona
    const archivo = document.getElementById('userfile');
    if (archivo && archivo.files.length > 0) {
        if (!validarArchivo(archivo, 'userfile')) {
            isValid = false;
        }
    }

    return isValid;
}

// Validaciones específicas para declaración reversa
function validarDeclaracionReversa(errores) {
    let isValid = true;

    // Validar serie
    const serie = document.getElementById('serieReversaDeclara').value.trim();
    if (!serie) {
        errores.push('Serie es requerida');
        marcarCampoInvalido('serieReversaDeclara', 'Por favor seleccione una serie');
        isValid = false;
    } else {
        marcarCampoValido('serieReversaDeclara');
    }

    // Validar archivo (requerido para declaración)
    const archivo = document.getElementById('fileReversaDecla');
    if (!archivo || archivo.files.length === 0) {
        errores.push('Archivo es requerido para la declaración');
        marcarCampoInvalido('fileReversaDecla', 'Por favor seleccione un archivo válido');
        isValid = false;
    } else {
        if (!validarArchivo(archivo, 'fileReversaDecla')) {
            isValid = false;
        }
    }

    return isValid;
}

// Función para validar archivos
function validarArchivo(inputFile, fieldId) {
    const file = inputFile.files[0];
    const maxSize = ModLogistica.getConfig('validation.archivo.maxSize') || (5 * 1024 * 1024); // 5MB default
    const allowedTypes = ModLogistica.getConfig('validation.archivo.allowedTypes') || [
        'image/jpeg', 'image/jpg', 'image/png', 'application/pdf',
        'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ];

    if (file.size > maxSize) {
        marcarCampoInvalido(fieldId, 'El archivo es demasiado grande. Tamaño máximo: 5MB');
        return false;
    }

    if (!allowedTypes.includes(file.type)) {
        marcarCampoInvalido(fieldId, 'Tipo de archivo no permitido. Use JPG, PNG, PDF, DOC o DOCX');
        return false;
    }

    marcarCampoValido(fieldId);
    return true;
}

// Función para marcar campo como inválido
function marcarCampoInvalido(fieldId, mensaje) {
    const field = document.getElementById(fieldId);
    const errorDiv = document.getElementById(fieldId + '_error');

    if (field) {
        field.classList.add('is-invalid');
        field.classList.remove('is-valid');
    }

    if (errorDiv) {
        errorDiv.textContent = mensaje;
        errorDiv.style.display = 'block';
    }
}

// Función para marcar campo como válido
function marcarCampoValido(fieldId) {
    const field = document.getElementById(fieldId);
    const errorDiv = document.getElementById(fieldId + '_error');

    if (field) {
        field.classList.add('is-valid');
        field.classList.remove('is-invalid');
    }

    if (errorDiv) {
        errorDiv.style.display = 'none';
    }
}

// Función para mostrar errores de validación
function mostrarErroresValidacion(errores) {
    let mensaje = 'Por favor corrija los siguientes errores:\n\n';
    errores.forEach(function(error, index) {
        mensaje += (index + 1) + '. ' + error + '\n';
    });
    alert(mensaje);
}

// Función para validar campo individual (validación en tiempo real)
function validarCampoIndividual(campo) {
    const fieldId = campo.id;
    const value = campo.value.trim();
    let isValid = true;
    let mensaje = '';

    const patronRut = ModLogistica.getConfig('validation.patterns.rut') || /^[0-9]{7,8}-[0-9kK]{1}$/;
    const patronOrden = ModLogistica.getConfig('validation.patterns.ordenTrabajo') || /^[0-9A-Za-z\-]+$/;
    const patronSerie = ModLogistica.getConfig('validation.patterns.serie') || /^[A-Za-z0-9\-]+$/;
    const minLength = ModLogistica.getConfig('validation.text.observacionesMinLength') || 10;
    const maxLength = ModLogistica.getConfig('validation.text.observacionesMaxLength') || 500;

    switch (fieldId) {
        case 'formOT_insta':
        case 'ordenReversa':
            if (!value) {
                isValid = false;
                mensaje = 'Este campo es requerido';
            } else if (!patronOrden.test(value)) {
                isValid = false;
                mensaje = 'Solo se permiten números, letras y guiones';
            }
            break;

        case 'rut_insta':
        case 'rutReversa':
            if (value && !patronRut.test(value)) {
                isValid = false;
                mensaje = 'Formato de RUT inválido (use: 12345678-9)';
            }
            break;

        case 'obs_insta':
            // obs_insta ahora es opcional
            if (value && value.length > maxLength) {
                isValid = false;
                mensaje = `Máximo ${maxLength} caracteres permitidos`;
            }
            break;
            
        case 'obs_rev_tra':
            if (!value) {
                isValid = false;
                mensaje = 'Este campo es requerido';
            } else if (value.length < minLength) {
                isValid = false;
                mensaje = `Mínimo ${minLength} caracteres requeridos`;
            } else if (value.length > maxLength) {
                isValid = false;
                mensaje = `Máximo ${maxLength} caracteres permitidos`;
            }
            break;

        case 'serieNewReversa':
            if (!value) {
                isValid = false;
                mensaje = 'Este campo es requerido';
            } else if (!patronSerie.test(value)) {
                isValid = false;
                mensaje = 'Solo se permiten letras, números y guiones';
            }
            break;

        case 'usuario_destino':
        case 'listReversa':
        case 'defaultSelectSm':
            if (!value) {
                isValid = false;
                mensaje = 'Por favor seleccione una opción';
            }
            break;

        case 'motivo_tran':
            if (!value) {
                isValid = false;
                mensaje = 'Este campo es requerido';
            } else if (value.length < ModLogistica.getConfig('validation.text.motivoMinLength')) {
                isValid = false;
                mensaje = 'Mínimo 10 caracteres requeridos';
            }
            break;
    }

    if (isValid) {
        marcarCampoValido(fieldId);
    } else {
        marcarCampoInvalido(fieldId, mensaje);
    }

    return isValid;
}

// Función para inicializar validación en tiempo real
function inicializarValidacionTiempoReal() {
    ModLogistica.debugLog('Inicializando validación en tiempo real');

    // Validación para campos de texto
    const textInputs = document.querySelectorAll('input[type="text"], textarea');
    textInputs.forEach(function(input) {
        input.addEventListener('blur', function() {
            validarCampoIndividual(this);
        });

        input.addEventListener('input', function() {
            // Limpiar estado de error mientras el usuario escribe
            if (this.classList.contains('is-invalid')) {
                this.classList.remove('is-invalid');
                const errorDiv = document.getElementById(this.id + '_error');
                if (errorDiv) {
                    errorDiv.style.display = 'none';
                }
            }
        });
    });

    // Validación para selects
    const selects = document.querySelectorAll('select');
    selects.forEach(function(select) {
        select.addEventListener('change', function() {
            validarCampoIndividual(this);
        });
    });

    // Validación para archivos
    const fileInputs = document.querySelectorAll('input[type="file"]');
    fileInputs.forEach(function(input) {
        input.addEventListener('change', function() {
            if (this.files.length > 0) {
                validarArchivo(this, this.id);
            }
        });
    });
}

// Función para resetear validaciones de un formulario
function resetearValidacionesFormulario(offcanvasId) {
    const offcanvas = document.getElementById(offcanvasId);
    if (offcanvas) {
        const campos = offcanvas.querySelectorAll('.is-valid, .is-invalid');
        campos.forEach(function(campo) {
            campo.classList.remove('is-valid', 'is-invalid');
        });

        const errores = offcanvas.querySelectorAll('.invalid-feedback');
        errores.forEach(function(error) {
            error.style.display = 'none';
        });
    }
}

// Actualización: Archivo modificado para transferencia al servidor - Corregidos errores de null patterns
console.log('✅ Validation.js - Sistema de validación cargado correctamente con validaciones defensivas');
