# Registro de Cambios y Documentación del Módulo de Logística

Este documento consolida todos los reportes, soluciones y documentación técnica relacionada con las mejoras y cambios implementados en el módulo de logística (`mod_logistica.php`).

---

## 1. Sistema SSE Independiente para `mod_logistica.php`

### Descripción

Se ha creado un sistema SSE (Server-Sent Events) completamente independiente para `mod_logistica.php` que no afecta ni modifica el sistema SSE existente (`sse_logistica.php`) utilizado por `Tecnico_Home_LOGIS_TEST_V2.php`.

### Archivos Creados/Modificados

#### 1. **sse_mod_logistica.php** (NUEVO)
- Archivo SSE independiente dedicado exclusivamente a `mod_logistica.php`
- Maneja las 4 tablas del módulo de logística
- Incluye soporte para `id_movimiento = 9` (rechazos) en la tabla de recepción
- Genera HTML específico para la estructura de `mod_logistica.php`

#### 2. **js/mod_logistica/sse-handler.js** (MODIFICADO)
- Actualizado para conectarse a `sse_mod_logistica.php` en lugar de `sse_logistica.php`
- Agregada función `updateTablaFaltante()` para manejar la tabla faltante
- Mejorado el manejo de `target_tbody` para mayor precisión
- Agregados event listeners para botones específicos de cada tabla

#### 3. **test_sse_mod_logistica.php** (NUEVO)
- Archivo de prueba para verificar el funcionamiento del nuevo SSE
- Incluye las 4 tablas con sus respectivos IDs
- Log en tiempo real de mensajes SSE
- Controles para iniciar/detener/limpiar

### Mapeo de Tablas

| Tabla en mod_logistica.php | ID del tbody | Tabla SSE | Descripción |
|----------------------------|--------------|-----------|-------------|
| Directa | `directaTableBody` | `TablaDirecta` | Materiales directos |
| Faltante | `faltanteTableBody` | `TablaFaltante` | Materiales faltantes |
| Recepción | `recepcionTableBody` | `tablaAsignacion` | Recepción de materiales |
| Reversa | `reversaTableBody` | `poolBodegaReversa` | Materiales reversa |

### Características Principales

#### 1. **Independencia Total**
- No modifica `sse_logistica.php` existente
- No afecta el funcionamiento de `Tecnico_Home_LOGIS_TEST_V2.php`
- Sistema completamente separado y modular

#### 2. **Soporte Completo para 4 Tablas**
- **Directa**: Materiales asignados directamente al técnico
- **Faltante**: Materiales marcados como faltantes (id_movimiento = 20)
- **Recepción**: Materiales pendientes de recepción (incluye rechazos id_movimiento = 9)
- **Reversa**: Materiales en proceso de reversa

#### 3. **Manejo de Rechazos**
- **PROBLEMA RESUELTO**: Los rechazos con `id_movimiento = 9` ahora aparecen correctamente en la tabla de recepción
- El SSE original excluía estos movimientos, el nuevo SSE los incluye específicamente

#### 4. **Estructura de Mensajes SSE**
```json
{
    "type": "update",
    "table": "TablaDirecta",
    "target_tbody": "directaTableBody",
    "serial": "ABC123",
    "id_movimiento": 1,
    "semantica": "Disponible",
    "html": "<tr>...</tr>"
}
```

### Consultas SQL por Tabla

#### Directa
```sql
SELECT A.*, tp_logis.Semantica 
FROM tb_logis_movimientos A 
LEFT JOIN tp_logis_movimientos tp_logis ON tp_logis.id = A.id_movimiento 
WHERE A.id > $lastId
AND id_movimiento NOT IN (2, 9, 10, 11, 18, 23) -- Exclude rejections
AND (
    (id_movimiento = 15 AND id_tecnico_origen = $userId) OR
    (id_movimiento IN (4, 19) AND ticket = $userId) OR
    (id_movimiento NOT IN (0, 15, 21, 3) AND id_tecnico_destino = $userId) OR
    (id_movimiento = 3 AND id_tecnico_origen = $userId) OR
    (id_movimiento IN (12, 24) AND ticket = $userId)
)
```

#### Faltante
```sql
SELECT A.*, tp_logis.Semantica 
FROM tb_logis_movimientos A 
LEFT JOIN tp_logis_movimientos tp_logis ON tp_logis.id = A.id_movimiento 
WHERE A.id > $lastId
AND id_movimiento IN (20) -- Faltante movements
AND (
    id_tecnico_destino = $userId OR 
    id_tecnico_origen = $userId OR
    ticket = $userId
)
```

#### Recepción (INCLUYE RECHAZOS DE BODEGA, EXCLUYE RECHAZOS DE TÉCNICO)
```sql
SELECT A.*, tp_logis.Semantica 
FROM tb_logis_movimientos A 
LEFT JOIN tp_logis_movimientos tp_logis ON tp_logis.id = A.id_movimiento 
WHERE A.id > $lastId
AND (
    (id_movimiento IN (0, 15, 21) AND id_tecnico_destino = $userId) OR
    (id_movimiento = 9 AND id_tecnico_destino = $userId) OR -- Include bodega rejections
    (id_movimiento = 24 AND ticket = $userId)
)
-- EXCLUDED: id_movimiento = 2 (rechazos técnico) - no deben reaparecer después de rechazar
```

#### Reversa
```sql
SELECT A.*, tp_logis.Semantica 
FROM tb_logis_movimientos A 
LEFT JOIN tp_logis_movimientos tp_logis ON tp_logis.id = A.id_movimiento 
WHERE A.id > $lastId
AND (
    (id_movimiento IN (6, 7, 11, 23) AND (id_tecnico_destino = $userId OR id_tecnico_origen = $userId)) OR
    (id_movimiento IN (22, 12, 11) AND ticket = $userId)
)
```

---

## 5. Modificación: Campo de Observaciones Opcional en Formulario de Instalación

**Fecha y Hora**: 2025-06-30 17:15:06

### Problema Identificado
El campo de observaciones en el formulario de instalación (botón "Declarar" en tabla directa) requería un mínimo de 10 caracteres, limitando la flexibilidad del usuario. Además, el texto de ayuda no se mostraba en color blanco como el resto del diseño.

### Cambios Implementados

#### 1. **Configuración JavaScript** (`js/mod_logistica/config.js`)
- **Línea 34**: Cambiado `observacionesMinLength: 10` a `observacionesMinLength: 0`

#### 2. **HTML del Formulario** (`mod_logistica.php`)
- **Líneas 630-637**: 
  - Removido atributo `required` del textarea `obs_insta`
  - Removido `minlength="10"`
  - Cambiado label de `Observaciones *` a `Observaciones (Opcional)`
  - Actualizado placeholder a "Describa los detalles de la instalación (opcional)"
  - Cambiado mensaje de ayuda a "Campo opcional, máximo 500 caracteres"

#### 3. **Validación JavaScript** (`js/mod_logistica/validation.js`)
- **Líneas 137-148**: Removida validación de mínimo de caracteres en función `validarInstalacion()`
- **Líneas 382-401**: Separado caso `obs_insta` para que sea opcional (solo valida máximo de caracteres si hay texto)

#### 4. **Estilo CSS** (`css/mod_logistica.css`)
- **Líneas 60-65**: Agregada clase `.form-text` con color blanco (`#f8f9fa`) y `!important`

### Resultado Final
- ✅ El campo de observaciones ahora puede estar completamente vacío
- ✅ La clase `.form-text` se muestra en color blanco consistente con el diseño
- ✅ Mantiene validación de máximo 500 caracteres cuando se ingresa texto
- ✅ Todas las referencias visuales indican claramente que el campo es opcional
- ✅ Preserva la funcionalidad existente sin afectar otros formularios

### Archivos Modificados
- `js/mod_logistica/config.js`
- `mod_logistica.php`
- `js/mod_logistica/validation.js`
- `css/mod_logistica.css`

---

## 6. Modificación: Eliminación de id_movimiento = 3 de Tabla Directa

**Fecha y Hora**: 2025-06-30 19:02:34

### Problema Identificado
Se requirió eliminar el `id_movimiento = 3` (transferencias) de la definición de la tabla directa para ajustar el flujo de negocio.

### Cambios Implementados

#### 1. **SSE Query Update** (`sse_mod_logistica.php`)
- **Líneas 78-80**: Removido `id_movimiento = 3` de las condiciones de inclusión
  - Eliminada condición: `(id_movimiento = 3 AND id_tecnico_origen = $userId)`
  - Actualizada condición: `id_movimiento NOT IN (0, 15, 21)` (removido el 3)

#### 2. **SSE Escalamientos** (`sse_mod_logistica.php`)
- **Línea 94**: Removido `id_movimiento = 3` de la lista de escalamientos que eliminan filas
  - Antes: `$escalamientos = [2, 3, 9, 10];`
  - Después: `$escalamientos = [2, 9, 10];`

### Resultado Final
- ✅ El `id_movimiento = 3` (transferencias) ya no aparece en la tabla directa
- ✅ Las transferencias no se procesan como escalamientos que eliminan filas
- ✅ Flujo de trabajo ajustado según requerimientos del negocio

### Archivos Modificados
- `sse_mod_logistica.php`

---

## 7. Resolución: Error Footer Modular en mod_logistica.php

**Fecha y Hora**: 2025-06-30 19:02:34

### Problema Identificado
El botón "+" del footer modular mostraba el mensaje "Formulario de materiales no encontrado" debido a conflictos entre el footer personalizado y el footer modular.

### Cambios Implementados

#### 1. **Eliminación de conflictos** (`mod_logistica.php`)
- **Líneas 456-458**: Removido div hardcodeado que interfería con el footer modular
- **Líneas 414-453**: Removido footer manual duplicado y overlays conflictivos

#### 2. **Inclusión correcta del footer modular** (`mod_logistica.php`)
- **Línea 1008**: Agregado `<?php include_once 'components/footer_modular.php'; ?>`

#### 3. **Formulario existente verificado** (`forms/form_materiales.php`)
- ✅ El formulario de materiales existe y está correctamente implementado

### Resultado Final
- ✅ El botón "+" del footer modular funciona correctamente
- ✅ Se abre el formulario "Solicitud de Materiales" sin errores
- ✅ Eliminados conflictos entre sistemas de footer

### Archivos Modificados
- `mod_logistica.php`

---

## 8. Mejora: Diagnóstico SSE para id_movimiento = 18

**Fecha y Hora**: 2025-06-30 23:55:00

### Problema Identificado
El SSE detectaba correctamente los movimientos con `id_movimiento = 18` (Rechaza Bodega Justificación) pero las filas no eran visibles en la interfaz, aunque se agregaban al DOM.

### Cambios Implementados

#### 1. **Inclusión de id_movimiento = 18 en SSE** (`sse_mod_logistica.php`)
- **Línea 74**: Removido `18` de la lista de exclusiones
- **Antes**: `AND id_movimiento NOT IN (2, 3, 9, 10, 11, 18, 23)`
- **Después**: `AND id_movimiento NOT IN (2, 3, 9, 10, 11, 23)`

#### 2. **Mejora de diagnóstico SSE** (`js/mod_logistica/sse-handler.js`)
- **Líneas 201-238**: Agregado logging detallado para diagnosticar problemas de visibilidad
- **Funcionalidad**: Auto-activación de pestaña directa si no está visible
- **Debug**: Información completa del estado de tablas y contenedores

### Funcionalidades Agregadas
- ✅ Detección automática de tabla directa no visible
- ✅ Auto-activación de pestaña directa cuando se recibe nuevo registro
- ✅ Logging detallado para diagnóstico de problemas
- ✅ Verificación de estado de contenedores y pestañas

### Resultado Esperado
- Los movimientos `id_movimiento = 18` ahora aparecen automáticamente en tabla directa
- Si la pestaña directa no está activa, se activa automáticamente
- Mejor diagnóstico de problemas de visibilidad en consola

### Archivos Modificados
- `sse_mod_logistica.php`
- `js/mod_logistica/sse-handler.js`

---

## 2. Solución Completa: Modal de Rechazo en `mod_logistica.php`

### Problema Identificado

El modal de rechazo no se mostraba al hacer clic en los botones de rechazo en la tabla de recepción debido a múltiples conflictos de funciones JavaScript.

### Problemas Encontrados

#### 1. Conflictos de Funciones JavaScript
- **Archivo `js/mod_logistica/ui.js` línea 304**: Sobrescribía `window.rechazoMaterial` con `mostrarModalRechazo`
- **Archivo `js/mod_logistica/handlers.js`**: Contenía la función correcta pero era sobrescrita
- **Múltiples definiciones**: Existían varias versiones de `rechazoMaterial` en diferentes archivos

#### 2. Orden de Carga de Scripts
- Los scripts se cargaban duplicados en `mod_logistica.php`
- `ui.js` se cargaba después de `handlers.js`, sobrescribiendo las funciones correctas

#### 3. Problemas de Diseño del Modal
- El modal no tenía una organización vertical clara
- Los elementos no estaban bien estructurados visualmente

### Soluciones Implementadas

#### 1. Resolución de Conflictos de Funciones

**A. Eliminación de Sobrescritura Conflictiva**
- **Archivo modificado**: `js/mod_logistica/ui.js`
- Se comentaron las líneas que asignaban `window.rechazoMaterial` y `window.Rechazocancelar` para evitar conflictos.

**B. Mejora de la Función Principal**
- **Archivo modificado**: `js/mod_logistica/handlers.js`
- Se agregaron logs detallados y se mejoró la lógica de configuración de campos.

**C. Sistema de Sobrescritura Automática**
- **Archivo modificado**: `mod_logistica.php`
- Se implementó una función para forzar la sobrescritura de funciones conflictivas, asegurando que se use la implementación correcta de `handlers.js`.

#### 2. Corrección del Orden de Carga
- **Archivo modificado**: `mod_logistica.php`
- Se eliminó la carga duplicada de scripts y se aseguró el orden correcto (`handlers.js` después de `ui.js`).

#### 3. Mejoras de Diseño del Modal
- **Archivo modificado**: `css/mod_logistica.css`
- Se aplicaron mejoras en la estructura del contenedor, la organización vertical, los elementos del formulario y el footer para una mejor UX.

### Resultado Final

- **Funcionalidades Corregidas**: El modal ahora se abre correctamente, ejecuta la función adecuada, y los botones son funcionales.
- **Mejoras de Diseño**: El layout es más intuitivo y el diseño es responsivo.
- **Robustez del Sistema**: Se implementó un sistema de sobrescritura para prevenir conflictos futuros y se añadieron logs para facilitar el debugging.

---

## 3. Actualización: Consistencia HTML entre SSE y Tablas Manuales

### Problema Identificado
El SSE estaba insertando filas con diseño de botones genérico que no coincidía con la funcionalidad específica de cada tabla en `mod_logistica.php`.

### Solución Implementada
Se actualizaron todas las funciones de generación HTML en `sse_mod_logistica.php` para que coincidan exactamente con la estructura generada por `tables.js`:

#### Cambios Principales:
1. **Tabla Directa**: Agregado onclick a botón de transferir con función `redirigirEnTransferencia`
2. **Tabla Faltante**: Actualizada estructura de botones para coincidir con tables.js
3. **Tabla Recepción**: Ya estaba correcta con onclick handlers
4. **Tabla Reversa**: Mantenida estructura correcta

#### Archivos Modificados:
- `sse_mod_logistica.php` - Funciones `generateDirectaTableRow()` y `generateFaltanteTableRow()`

---

## 4. Reporte: Flujo del Botón de Historial en `mod_logistica.php`

### Objetivo
Documentar los archivos y el flujo de interacción del botón "Historial" en la interfaz del módulo de logística.

### Descripción General
El botón de historial muestra un registro detallado de los movimientos de una serie específica en un panel lateral deslizante (offcanvas).

### Archivos Clave Involucrados

1.  **`mod_logistica.php`**: Capa de presentación que contiene la estructura HTML de las tablas y el offcanvas del historial.
2.  **`js/mod_logistica/tables.js`**: Lógica de la interfaz que detecta el clic en el botón de historial y extrae la serie.
3.  **`js/mod_logistica/api.js`**: Capa de datos que gestiona la comunicación con el backend para obtener los datos del historial.
4.  **`GET_LOGISTICA.php`**: Endpoint del servidor que recibe la solicitud, consulta la base de datos y retorna los datos en formato JSON.
5.  **`con_db.php`**: Archivo de utilidad para la conexión a la base de datos.
6.  **`js/mod_logistica/ui.js`**: Capa de presentación que renderiza dinámicamente los datos del historial en el offcanvas.
7.  **`css/mod_logistica.css`**: Define la apariencia visual de los componentes del historial.

### Diagrama de Flujo del Botón de Historial

```mermaid
graph TD
    A[Usuario en mod_logistica.php] --> B{Clic en Botón "Historial"};
    B --> C[js/mod_logistica/tables.js: Detecta clic y obtiene 'serie'];
    C --> D[js/mod_logistica/tables.js: Abre offcanvasHistorial];
    C --> E[js/mod_logistica/api.js: Llama a GET_LOGISTICA.php con 'serie'];
    E --> F[GET_LOGISTICA.php: Recibe 'serie'];
    F --> G[GET_LOGISTICA.php: Incluye con_db.php];
    G --> H[con_db.php: Establece conexión DB];
    H --> I[GET_LOGISTICA.php: Consulta DB por historial de 'serie'];
    I --> J[Base de Datos: Retorna datos de historial];
    J --> F;
    F --> K[GET_LOGISTICA.php: Formatea y retorna JSON];
    K --> E;
    E --> L[js/mod_logistica/ui.js: Recibe datos JSON];
    L --> M[js/mod_logistica/ui.js: Renderiza datos en #webHistorial];
    M --> N[offcanvasHistorial: Muestra historial formateado];
```
