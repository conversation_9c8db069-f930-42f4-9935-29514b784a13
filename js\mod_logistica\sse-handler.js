/**
 * sse-handler.js
 * Manejador de Server-Sent Events para el módulo de logística
 * Conecta con sse_logistica.php para recibir notificaciones en tiempo real
 */

// Variables globales para SSE (usando nombres únicos para evitar conflictos)
let logisticaEventSource = null;
let logisticaReconnectAttempts = 0;
const MAX_RECONNECT_ATTEMPTS = 5;

/**
 * Inicializar conexión SSE
 */
function initLogisticaSSE() {
    try {
        // Cerrar conexión existente si existe
        if (logisticaEventSource) {
            logisticaEventSource.close();
            console.log('🔌 Cerrando conexión SSE anterior');
        }

        // Obtener ID de usuario
        let userId = null;
        
        // Intentar obtener desde configuración global
        if (window.ModLogisticaConfig && window.ModLogisticaConfig.user && window.ModLogisticaConfig.user.id) {
            userId = window.ModLogisticaConfig.user.id;
        }
        
        // Fallback a variable global userId
        if (!userId && typeof window.userId !== 'undefined') {
            userId = window.userId;
        }
        
        // Verificar que tenemos un userId válido
        if (!userId) {
            console.error('❌ No se pudo obtener el ID de usuario para SSE');
            return;
        }

        // Construir URL de conexión SSE - USANDO NUEVO ARCHIVO INDEPENDIENTE
        const params = new URLSearchParams({
            user_id: userId,
            timestamp: Date.now() // Para evitar cache
        });

        const url = 'sse_mod_logistica.php?' + params.toString();
        console.log('🔗 Conectando SSE:', url);
        
        // Crear nueva conexión EventSource
        logisticaEventSource = new EventSource(url);

        // Evento: Conexión exitosa
        logisticaEventSource.onopen = function() {
            console.log('✅ Conexión SSE de logística establecida correctamente');
            logisticaReconnectAttempts = 0;
            
            // Mostrar indicador de conexión activa si existe
            updateSSEStatus('connected');
        };
        
        // Evento: Mensaje recibido
        logisticaEventSource.onmessage = function(event) {
            try {
                const data = JSON.parse(event.data);
                console.log('📨 Mensaje SSE recibido:', data);
                processLogisticaSSEMessage(data);
            } catch (error) {
                console.error('❌ Error al procesar mensaje SSE:', error, 'Datos:', event.data);
            }
        };

        // Evento: Error de conexión
        logisticaEventSource.onerror = function(error) {
            console.error('❌ Error en conexión SSE:', error);

            updateSSEStatus('error');

            if (logisticaEventSource) {
                logisticaEventSource.close();
                console.log('🔌 Conexión SSE cerrada por error');
            }

            // Reconexión automática con backoff exponencial
            const delay = Math.min(1000 * Math.pow(2, logisticaReconnectAttempts), 30000);
            console.log(`🔄 Reintentando conexión en ${delay/1000}s... (Intento ${logisticaReconnectAttempts+1}/${MAX_RECONNECT_ATTEMPTS})`);

            logisticaReconnectAttempts++;
            if (logisticaReconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
                setTimeout(initLogisticaSSE, delay);
            } else {
                console.error('🚫 Máximo de intentos alcanzado. SSE deshabilitado.');
                updateSSEStatus('failed');
                
                // Mostrar notificación al usuario
                if (typeof mostrarNotificacion === 'function') {
                    mostrarNotificacion('Se perdió la conexión en tiempo real. Recarga la página.', 'warning');
                }
            }
        };
        
    } catch (e) {
        console.error('❌ Error al inicializar SSE de logística:', e);
        updateSSEStatus('error');
    }
}

/**
 * Procesar mensajes SSE específicos de logística
 */
function processLogisticaSSEMessage(data) {
    if (!data) return;

    // Mensaje de debug/log
    if (data.type === 'log') {
        console.log('📋 SSE Log:', data.message);
        return;
    }

    // Mensaje de keepalive
    if (data.type === 'keepalive') {
        console.log('💓 SSE Keepalive:', data.message);
        return;
    }

    // Actualización de tabla directa
    if (data.table === 'TablaDirecta' && data.target_tbody === 'directaTableBody') {
        if (data.type === 'remove') {
            console.log('🗑️ Eliminando fila de TablaDirecta (escalamiento)');
            removeFromTablaDirecta(data);
        } else if (data.html) {
            console.log('🔄 Actualizando TablaDirecta con nuevo registro');
            updateTablaDirecta(data);
        }
        return;
    }

    // Actualización de tabla faltante
    if (data.table === 'TablaFaltante' && data.target_tbody === 'faltanteTableBody' && data.html) {
        console.log('🔄 Actualizando TablaFaltante con nuevo registro');
        updateTablaFaltante(data);
        return;
    }

    // Actualización de tabla de recepción
    if (data.table === 'tablaAsignacion' && data.target_tbody === 'recepcionTableBody' && data.html) {
        console.log('🔄 Actualizando tablaAsignacion/recepcionTableBody con nuevo registro');
        updateTablaAsignacion(data);
        return;
    }

    // Actualización de tabla reversa
    if (data.table === 'poolBodegaReversa' && data.target_tbody === 'reversaTableBody' && data.html) {
        console.log('🔄 Actualizando poolBodegaReversa/reversaTableBody con nuevo registro');
        updateTablaReversa(data);
        return;
    }

    // Notificación general
    if (data.type === 'notification') {
        console.log('🔔 Notificación SSE:', data.message);

        if (typeof mostrarNotificacion === 'function') {
            mostrarNotificacion(data.message, data.level || 'info');
        }
        return;
    }

    console.log('❓ Mensaje SSE no procesado:', data);
}

/**
 * Actualizar tabla directa con nuevo registro
 */
function updateTablaDirecta(data) {
    // Usar target_tbody del mensaje SSE si está disponible
    const tbodyId = data.target_tbody || 'directaTableBody';
    const tbody = document.getElementById(tbodyId);
    if (!tbody) {
        console.warn('⚠️ tbody ' + tbodyId + ' no encontrado');
        return;
    }
    
    // Remover filas de loading si existen
    const loadingRows = tbody.querySelectorAll('.loading-row');
    loadingRows.forEach(row => row.remove());
    
    // Crear nueva fila desde HTML recibido
    console.log('🔍 HTML recibido del SSE:', data.html);
    
    // Para elementos TR, necesitamos usar un tbody temporal
    const tempTable = document.createElement('table');
    const tempTbody = document.createElement('tbody');
    tempTable.appendChild(tempTbody);
    tempTbody.innerHTML = data.html;
    const newRow = tempTbody.firstElementChild;
    
    console.log('🔍 Nueva fila creada:', newRow);
    
    if (newRow) {
        // Debug: Verificar estado de la tabla
        const tablaDirecta = document.getElementById('directaTable');
        const containerDirecta = document.querySelector('.table-container[data-table="directa"]');

        console.log('🔍 Debug tabla directa:', {
            tablaExiste: !!tablaDirecta,
            tablaVisible: tablaDirecta?.offsetParent !== null,
            containerExiste: !!containerDirecta,
            containerVisible: containerDirecta?.offsetParent !== null,
            containerDisplay: containerDirecta ? window.getComputedStyle(containerDirecta).display : 'N/A',
            tbodyFilas: tbody.children.length,
            pestañaActiva: document.querySelector('.tab-button.active')?.textContent || 'ninguna'
        });

        // Agregar animación de nuevo registro
        newRow.style.backgroundColor = '#d4edda';
        newRow.style.transition = 'background-color 3s ease';

        // Insertar al inicio de la tabla
        tbody.insertBefore(newRow, tbody.firstChild);

        console.log('✅ Nueva fila agregada a directaTable. Total filas:', tbody.children.length);

        // Si la tabla directa no está visible, activarla
        if (containerDirecta && window.getComputedStyle(containerDirecta).display === 'none') {
            console.log('🔄 Tabla directa no visible, intentando activarla...');
            const botonDirecta = document.querySelector('.tab-button[data-table="directa"]');
            if (botonDirecta) {
                botonDirecta.click();
                console.log('🔄 Pestaña directa activada automáticamente');
            }
        }

        // Remover animación después de 3 segundos
        setTimeout(() => {
            newRow.style.backgroundColor = '';
        }, 3000);
        
        // Reinicializar eventos de botones para la nueva fila
        if (typeof window.ModLogisticaTables !== 'undefined' && window.ModLogisticaTables.initTableEvents) {
            window.ModLogisticaTables.initTableEvents();
        } else if (typeof initTablaEvents === 'function') {
            initTablaEvents();
        }
        
        // Reinicializar eventos específicos para los nuevos botones
        initNewRowEvents(newRow);
        
        console.log('✅ Nueva fila agregada a directaTable');
    }
}

/**
 * Eliminar fila de tabla directa (para escalamientos)
 */
function removeFromTablaDirecta(data) {
    const tbodyId = data.target_tbody || 'directaTableBody';
    const tbody = document.getElementById(tbodyId);
    if (!tbody) {
        console.warn('⚠️ tbody ' + tbodyId + ' no encontrado');
        return;
    }

    // Buscar la fila por serie
    const rows = tbody.querySelectorAll('tr[data-serial="' + data.serial + '"]');

    if (rows.length > 0) {
        rows.forEach(row => {
            // Agregar animación de eliminación
            row.style.backgroundColor = '#f8d7da';
            row.style.transition = 'opacity 0.5s ease, background-color 0.5s ease';
            row.style.opacity = '0.5';

            // Eliminar después de la animación
            setTimeout(() => {
                row.remove();
                console.log('✅ Fila eliminada de directaTable:', data.serial);
            }, 500);
        });
    } else {
        console.warn('⚠️ No se encontró fila con serie:', data.serial);
    }
}

/**
 * Actualizar tabla faltante con nuevo registro
 */
function updateTablaFaltante(data) {
    const tbody = document.getElementById('faltanteTableBody');
    if (!tbody) {
        console.warn('⚠️ tbody faltanteTableBody no encontrado');
        return;
    }

    // Remover filas de loading si existen
    const loadingRows = tbody.querySelectorAll('.loading-row');
    loadingRows.forEach(row => row.remove());

    // Crear nueva fila desde HTML recibido
    console.log('🔍 HTML recibido del SSE para faltante:', data.html);

    // Para elementos TR, necesitamos usar un tbody temporal
    const tempTable = document.createElement('table');
    const tempTbody = document.createElement('tbody');
    tempTable.appendChild(tempTbody);
    tempTbody.innerHTML = data.html;
    const newRow = tempTbody.firstElementChild;

    console.log('🔍 Nueva fila faltante creada:', newRow);

    if (newRow) {
        // Agregar animación de nuevo registro
        newRow.style.backgroundColor = '#fff3cd';
        newRow.style.transition = 'background-color 3s ease';

        // Insertar al inicio de la tabla
        tbody.insertBefore(newRow, tbody.firstChild);

        // Remover animación después de 3 segundos
        setTimeout(() => {
            newRow.style.backgroundColor = '';
        }, 3000);

        // Reinicializar eventos de botones para la nueva fila
        if (typeof window.ModLogisticaTables !== 'undefined' && window.ModLogisticaTables.initTableEvents) {
            window.ModLogisticaTables.initTableEvents();
        } else if (typeof initTablaEvents === 'function') {
            initTablaEvents();
        }

        // Reinicializar eventos específicos para los nuevos botones
        initNewRowEvents(newRow);

        console.log('✅ Nueva fila agregada a faltanteTable');
    }
}

/**
 * Actualizar tabla de asignación/recepción con nuevo registro
 */
function updateTablaAsignacion(data) {
    // Usar target_tbody del mensaje SSE si está disponible
    const tbodyId = data.target_tbody || 'recepcionTableBody';
    const tbody = document.getElementById(tbodyId);
    if (!tbody) {
        console.warn('⚠️ tbody ' + tbodyId + ' no encontrado');
        return;
    }
    
    // Remover filas de loading si existen
    const loadingRows = tbody.querySelectorAll('.loading-row');
    loadingRows.forEach(row => row.remove());
    
    // Similar implementación que TablaDirecta
    // Para elementos TR, necesitamos usar un tbody temporal
    const tempTable = document.createElement('table');
    const tempTbody = document.createElement('tbody');
    tempTable.appendChild(tempTbody);
    tempTbody.innerHTML = data.html;
    const newRow = tempTbody.firstElementChild;
    
    if (newRow) {
        newRow.style.backgroundColor = '#d4edda';
        newRow.style.transition = 'background-color 3s ease';
        
        tbody.insertBefore(newRow, tbody.firstChild);
        
        setTimeout(() => {
            newRow.style.backgroundColor = '';
        }, 3000);
        
        // Reinicializar eventos de botones para la nueva fila
        if (typeof window.ModLogisticaTables !== 'undefined' && window.ModLogisticaTables.initTableEvents) {
            window.ModLogisticaTables.initTableEvents();
        } else if (typeof initTablaEvents === 'function') {
            initTablaEvents();
        }
        
        // Reinicializar eventos específicos para los nuevos botones
        initNewRowEvents(newRow);
        
        console.log('✅ Nueva fila agregada a recepcionTable');
    }
}

/**
 * Actualizar tabla reversa con nuevo registro
 */
function updateTablaReversa(data) {
    // Usar target_tbody del mensaje SSE si está disponible
    const tbodyId = data.target_tbody || 'reversaTableBody';
    const tbody = document.getElementById(tbodyId);
    if (!tbody) {
        console.warn('⚠️ tbody ' + tbodyId + ' no encontrado');
        return;
    }
    
    // Remover filas de loading si existen
    const loadingRows = tbody.querySelectorAll('.loading-row');
    loadingRows.forEach(row => row.remove());
    
    // Similar implementación que las otras tablas
    // Para elementos TR, necesitamos usar un tbody temporal
    const tempTable = document.createElement('table');
    const tempTbody = document.createElement('tbody');
    tempTable.appendChild(tempTbody);
    tempTbody.innerHTML = data.html;
    const newRow = tempTbody.firstElementChild;
    
    if (newRow) {
        newRow.style.backgroundColor = '#d4edda';
        newRow.style.transition = 'background-color 3s ease';
        
        tbody.insertBefore(newRow, tbody.firstChild);
        
        setTimeout(() => {
            newRow.style.backgroundColor = '';
        }, 3000);
        
        // Reinicializar eventos de botones para la nueva fila
        if (typeof window.ModLogisticaTables !== 'undefined' && window.ModLogisticaTables.initTableEvents) {
            window.ModLogisticaTables.initTableEvents();
        } else if (typeof initTablaEvents === 'function') {
            initTablaEvents();
        }
        
        // Reinicializar eventos específicos para los nuevos botones
        initNewRowEvents(newRow);
        
        console.log('✅ Nueva fila agregada a reversaTable');
    }
}

/**
 * Actualizar indicador de estado SSE
 */
function updateSSEStatus(status) {
    const statusElement = document.getElementById('sse-status');
    if (!statusElement) return;
    
    statusElement.className = 'sse-status sse-' + status;
    
    switch (status) {
        case 'connected':
            statusElement.textContent = '🟢 Conectado';
            statusElement.title = 'Recibiendo actualizaciones en tiempo real';
            break;
        case 'error':
            statusElement.textContent = '🟡 Reconectando...';
            statusElement.title = 'Intentando reconectar';
            break;
        case 'failed':
            statusElement.textContent = '🔴 Desconectado';
            statusElement.title = 'Sin conexión en tiempo real';
            break;
        default:
            statusElement.textContent = '⚪ Iniciando...';
            statusElement.title = 'Iniciando conexión';
    }
}

/**
 * Inicializar eventos para nuevas filas agregadas por SSE
 */
function initNewRowEvents(row) {
    if (!row) return;
    
    // Buscar botones dentro de la nueva fila
    const historialBtns = row.querySelectorAll('.historial-btn');
    const acceptBtns = row.querySelectorAll('.accept-btn');
    const rejectBtns = row.querySelectorAll('.reject-btn');
    const declararBtns = row.querySelectorAll('.declarar-btn');
    const transferirBtns = row.querySelectorAll('.transfiere-btn');
    const justificarBtns = row.querySelectorAll('.justificar-btn');
    const declararRevBtns = row.querySelectorAll('.declarar-rev-btn');
    const transferirRevBtns = row.querySelectorAll('.transferir-rev-btn');
    
    // Configurar eventos para botones de historial
    historialBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const serie = this.dataset.serie;
            const item = this.dataset.item || this.dataset.idMovimiento;
            const idMovimiento = this.dataset.idMovimiento;
            
            if (typeof window.redirigirEnTransferencia === 'function') {
                window.redirigirEnTransferencia(serie, item, idMovimiento, 'VER');
            }
        });
    });
    
    // Configurar eventos para botones de aceptar
    acceptBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const serie = this.dataset.serie;
            const ticket = this.dataset.ticket;
            const idMovimiento = this.dataset.idMovimiento;
            
            if (typeof window.actualizarRegistro === 'function') {
                window.actualizarRegistro(serie, ticket, idMovimiento, 'ACEPTA');
            }
        });
    });
    
    // Configurar eventos para botones de rechazar
    rejectBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const serie = this.dataset.serie;
            const ticket = this.dataset.ticket;
            const idDestino = this.dataset.idDestino;
            
            if (typeof window.rechazoMaterial === 'function') {
                window.rechazoMaterial(serie, ticket, idDestino, 'RECHAZA');
            }
        });
    });
    
    // Configurar eventos para botones de declarar
    declararBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const serie = this.dataset.serie;
            const idMovimiento = this.dataset.idMovimiento;
            
            if (typeof window.redirigirEnTransferencia === 'function') {
                window.redirigirEnTransferencia(serie, '', idMovimiento, 'INSTALA');
            }
        });
    });
    
    // Configurar eventos para botones de transferir
    transferirBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const serie = this.dataset.serie;
            const idMovimiento = this.dataset.idMovimiento;

            if (typeof window.redirigirEnTransferencia === 'function') {
                window.redirigirEnTransferencia(serie, '', idMovimiento, 'TRANSFIERE');
            }
        });
    });

    // Configurar eventos para botones de justificar (tabla faltante)
    justificarBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const serie = this.dataset.serie;

            if (typeof window.rechazoMaterial === 'function') {
                window.rechazoMaterial(serie, '', '', 'JUSTIFICA_FALTANTE');
            }
        });
    });

    // Configurar eventos para botones de declarar reversa
    declararRevBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const serie = this.dataset.serie;
            const idMovimiento = this.dataset.idMovimiento;

            if (typeof window.redirigirEnTransferencia === 'function') {
                window.redirigirEnTransferencia(serie, '', idMovimiento, 'ENTREGA_REV');
            }
        });
    });

    // Configurar eventos para botones de transferir reversa
    transferirRevBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const serie = this.dataset.serie;
            const idMovimiento = this.dataset.idMovimiento;

            if (typeof window.redirigirEnTransferencia === 'function') {
                window.redirigirEnTransferencia(serie, '', idMovimiento, 'TRANSFIERE_REV');
            }
        });
    });

    console.log('🔗 Eventos configurados para nueva fila SSE');
}

/**
 * Cerrar conexión SSE al salir de la página
 */
function closeLogisticaSSE() {
    if (logisticaEventSource) {
        logisticaEventSource.close();
        logisticaEventSource = null;
        console.log('🔌 Conexión SSE cerrada');
        updateSSEStatus('disconnected');
    }
}

// Función para inicializar SSE externamente
function startLogisticaSSE() {
    console.log('🚀 Inicializando SSE de logística...');
    
    // Esperar un poco para que se carguen las configuraciones
    setTimeout(initLogisticaSSE, 1000);
}

// Cerrar conexión al salir de la página
window.addEventListener('beforeunload', closeLogisticaSSE);

// Auto-inicializar SSE si se carga directamente
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', startLogisticaSSE);
} else {
    // El DOM ya está cargado
    startLogisticaSSE();
}

// Exportar funciones al scope global
window.LogisticaSSE = {
    start: startLogisticaSSE,
    init: initLogisticaSSE,
    close: closeLogisticaSSE,
    updateStatus: updateSSEStatus
};

// Log de carga del módulo
console.log('📡 sse-handler.js cargado - Sistema SSE para logística inicializado');